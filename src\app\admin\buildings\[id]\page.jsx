// src/app/admin/buildings/[id]/page.jsx
// View building details page

"use client";

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';

export default function ViewBuildingPage({ params }) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [building, setBuilding] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  // Check for success messages
  useEffect(() => {
    if (searchParams.get('updated') === 'true') {
      setShowSuccessMessage(true);
      setTimeout(() => setShowSuccessMessage(false), 5000);
    }
  }, [searchParams]);

  // Redirect if not authenticated
  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // if (!session) {
  //   router.push('/auth/signin');
  //   return null;
  // }

  // Fetch building data
  useEffect(() => {
    const fetchBuilding = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/buildings/${params.id}`);
        
        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Building not found');
          }
          throw new Error('Failed to fetch building data');
        }

        const data = await response.json();
        setBuilding(data.building);
        setError('');
      } catch (err) {
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    if (params.id) {
      fetchBuilding();
    }
  }, [params.id]);

  // Handle delete
  const handleDelete = async () => {
    if (!confirm(`Are you sure you want to delete "${building.projectTitle}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await fetch(`/api/buildings/${params.id}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error('Failed to delete building');
      }

      router.push('/admin/buildings?deleted=true');
    } catch (err) {
      alert('Error deleting building: ' + err.message);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-lg shadow-sm border p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-600 mt-2">Loading building data...</p>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-red-50 border border-red-200 rounded-md p-6 text-center">
            <div className="flex justify-center">
              <svg className="h-12 w-12 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-red-800 mt-2">Error Loading Building</h3>
            <p className="text-red-700 mt-1">{error}</p>
            <div className="mt-4">
              <Link
                href="/admin/buildings"
                className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
              >
                Back to Buildings
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!building) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Success Message */}
        {showSuccessMessage && (
          <div className="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-green-800">
                  Building updated successfully!
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center space-x-4 mb-4">
                <Link
                  href="/admin/buildings"
                  className="text-blue-600 hover:text-blue-800 flex items-center"
                >
                  <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                  Back to Buildings
                </Link>
              </div>
              <h1 className="text-3xl font-bold text-gray-900">{building.projectTitle}</h1>
              <p className="text-gray-600 mt-2">{building.buildingTitle}</p>
            </div>
            <div className="flex space-x-3">
              <Link
                href={`/admin/buildings/${params.id}/edit`}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Edit Building
              </Link>
              {session?.user?.role === 'admin' && (
                <button
                  onClick={handleDelete}
                  className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                >
                  Delete Building
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Building Details */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Basic Information</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Building Type</label>
                  <span className="inline-flex px-2 py-1 text-sm font-semibold rounded-full bg-blue-100 text-blue-800 mt-1">
                    {building.buildingType}
                  </span>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Price</label>
                  <p className="text-lg font-semibold text-gray-900 mt-1">{building.price}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Building Role</label>
                  <p className="text-gray-900 mt-1">{building.buildingRole}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Created</label>
                  <p className="text-gray-900 mt-1">{new Date(building.createdAt).toLocaleDateString()}</p>
                </div>
              </div>
            </div>

            {/* Description */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Description</h2>
              <p className="text-gray-700 leading-relaxed">{building.desc}</p>
            </div>

            {/* Features */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Features</h2>
              <p className="text-gray-700 leading-relaxed">{building.features}</p>
            </div>

            {/* Building Highlights */}
            {building.buildingHighlights && building.buildingHighlights.length > 0 && (
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Building Highlights</h2>
                <div className="space-y-4">
                  {building.buildingHighlights.map((highlight, index) => (
                    <div key={index} className="border-l-4 border-blue-500 pl-4">
                      <h3 className="font-medium text-gray-900">{highlight.title}</h3>
                      <p className="text-gray-700 mt-1">{highlight.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Building Summary */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Building Summary</h2>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Length:</span>
                  <span className="font-medium">{building.buildingSummary.length}m</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Width:</span>
                  <span className="font-medium">{building.buildingSummary.width}m</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Bedrooms:</span>
                  <span className="font-medium">{building.buildingSummary.beds}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Bathrooms:</span>
                  <span className="font-medium">{building.buildingSummary.baths}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Levels:</span>
                  <span className="font-medium">{building.buildingSummary.levels}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Car Spaces:</span>
                  <span className="font-medium">{building.buildingSummary.cars}</span>
                </div>
              </div>
            </div>

            {/* Tags */}
            {building.tags && building.tags.length > 0 && (
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Tags</h2>
                <div className="flex flex-wrap gap-2">
                  {building.tags.map((tag, index) => (
                    <span key={index} className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Colors */}
            {building.color && building.color.length > 0 && (
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Colors</h2>
                <div className="flex flex-wrap gap-2">
                  {building.color.map((color, index) => (
                    <span key={index} className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                      {color}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Collections */}
            {building.collections && building.collections.length > 0 && (
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Collections</h2>
                <div className="flex flex-wrap gap-2">
                  {building.collections.map((collection, index) => (
                    <span key={index} className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                      {collection}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* File Uploads Summary */}
        <div className="mt-8 bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Uploaded Files</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[
              { key: 'renders', label: 'Renders' },
              { key: 'drawings', label: 'Drawings' },
              { key: '_360sImages', label: '360° Images' },
              { key: 'modelsFiles', label: 'Model Files' },
              { key: 'hideLevel', label: 'Hide Level Files' },
              { key: 'supportFiles', label: 'Support Files' },
              { key: 'roomSnaps', label: 'Room Snaps' },
              { key: 'presentationDrawings', label: 'Presentation Drawings' },
              { key: 'constructionDrawingsPdf', label: 'Construction Drawings (PDF)' },
              { key: 'constructionDrawingsDwg', label: 'Construction Drawings (DWG)' }
            ].map(({ key, label }) => (
              <div key={key} className="p-3 border border-gray-200 rounded-md">
                <h3 className="font-medium text-gray-900">{label}</h3>
                <p className="text-sm text-gray-600 mt-1">
                  {building[key] ? building[key].length : 0} files
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

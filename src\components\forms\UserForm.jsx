// src/components/forms/UserForm.jsx
// User form component for creating and editing users

"use client";

import { useState, useEffect } from 'react';
import TextInput from './TextInput';
import SelectInput from './SelectInput';
import ArrayInput from './ArrayInput';

/**
 * UserForm Component
 * @param {Object} props
 * @param {Object} props.initialData - Initial form data for editing
 * @param {Function} props.onSubmit - Form submission handler
 * @param {Function} props.onCancel - Form cancellation handler
 * @param {boolean} props.isLoading - Loading state
 * @param {string} props.mode - 'create' or 'edit'
 * @param {boolean} props.canEditRole - Whether user can edit role field
 */
export default function UserForm({
  initialData = null,
  onSubmit,
  onCancel,
  isLoading = false,
  mode = 'create',
  canEditRole = true
}) {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    role: 'user',
    invites: [],
    invoices: [],
    recipets: [],
    messgages: [],
    image: '',
    emailVerified: false
  });

  const [errors, setErrors] = useState({});
  const [isDirty, setIsDirty] = useState(false);

  // Initialize form with existing data for editing
  useEffect(() => {
    if (initialData && mode === 'edit') {
      setFormData(prev => ({
        ...prev,
        ...initialData,
        password: '', // Don't pre-fill password
        confirmPassword: '',
        emailVerified: !!initialData.emailVerified
      }));
    }
  }, [initialData, mode]);

  const handleInputChange = (value, name) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setIsDirty(true);

    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }

    // Clear confirm password error if password changes
    if (name === 'password' && errors.confirmPassword) {
      setErrors(prev => ({
        ...prev,
        confirmPassword: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Required fields
    if (!formData.name || formData.name.trim() === '') {
      newErrors.name = 'Name is required';
    }

    if (!formData.email || formData.email.trim() === '') {
      newErrors.email = 'Email is required';
    } else {
      // Email format validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email)) {
        newErrors.email = 'Invalid email format';
      }
    }

    // Password validation (required for create, optional for edit)
    if (mode === 'create' || formData.password) {
      if (!formData.password) {
        newErrors.password = 'Password is required';
      } else if (formData.password.length < 6) {
        newErrors.password = 'Password must be at least 6 characters';
      }

      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
    }

    // Role validation
    const validRoles = ['user', 'admin'];
    if (!validRoles.includes(formData.role)) {
      newErrors.role = 'Invalid role';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    // Prepare data for submission
    const submitData = { ...formData };
    delete submitData.confirmPassword;

    // Don't send empty password for edit mode
    if (mode === 'edit' && !submitData.password) {
      delete submitData.password;
    }

    await onSubmit(submitData);
  };

  const handleReset = () => {
    if (mode === 'create') {
      setFormData({
        name: '',
        email: '',
        password: '',
        confirmPassword: '',
        role: 'user',
        invites: [],
        invoices: [],
        recipets: [],
        messgages: [],
        image: '',
        emailVerified: false
      });
    } else {
      setFormData(prev => ({
        ...prev,
        ...initialData,
        password: '',
        confirmPassword: '',
        emailVerified: !!initialData.emailVerified
      }));
    }
    setErrors({});
    setIsDirty(false);
  };

  const roleOptions = [
    { value: 'user', label: 'User' },
    { value: 'admin', label: 'Admin' }
  ];

  return (
    <form onSubmit={handleSubmit} className="max-w-2xl mx-auto space-y-8">
      {/* Basic Information Section */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Basic Information</h2>
        
        <div className="space-y-6">
          <TextInput
            name="name"
            label="Full Name"
            value={formData.name}
            onChange={handleInputChange}
            required
            error={errors.name}
            placeholder="Enter full name"
            maxLength={60}
          />
          
          <TextInput
            name="email"
            label="Email Address"
            type="email"
            value={formData.email}
            onChange={handleInputChange}
            required
            error={errors.email}
            placeholder="Enter email address"
          />

          {canEditRole && (
            <SelectInput
              name="role"
              label="User Role"
              value={formData.role}
              onChange={handleInputChange}
              options={roleOptions}
              required
              error={errors.role}
              helpText="Admin users have full access to all features"
            />
          )}
        </div>
      </div>

      {/* Authentication Section */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">
          {mode === 'create' ? 'Set Password' : 'Change Password'}
        </h2>
        
        <div className="space-y-6">
          <TextInput
            name="password"
            label="Password"
            type="password"
            value={formData.password}
            onChange={handleInputChange}
            required={mode === 'create'}
            error={errors.password}
            placeholder={mode === 'create' ? 'Enter password' : 'Leave blank to keep current password'}
            helpText={mode === 'edit' ? 'Leave blank to keep current password' : 'Minimum 6 characters'}
          />
          
          <TextInput
            name="confirmPassword"
            label="Confirm Password"
            type="password"
            value={formData.confirmPassword}
            onChange={handleInputChange}
            required={mode === 'create' || !!formData.password}
            error={errors.confirmPassword}
            placeholder="Confirm password"
          />
        </div>
      </div>

      {/* Additional Information Section */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Additional Information</h2>
        
        <div className="space-y-6">
          <TextInput
            name="image"
            label="Profile Image URL"
            value={formData.image}
            onChange={handleInputChange}
            error={errors.image}
            placeholder="https://example.com/profile.jpg"
            helpText="Optional profile image URL"
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <ArrayInput
              name="invites"
              label="Invites"
              value={formData.invites}
              onChange={handleInputChange}
              placeholder="Add invite..."
              helpText="User invitations"
            />
            
            <ArrayInput
              name="invoices"
              label="Invoices"
              value={formData.invoices}
              onChange={handleInputChange}
              placeholder="Add invoice..."
              helpText="User invoices"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <ArrayInput
              name="recipets"
              label="Receipts"
              value={formData.recipets}
              onChange={handleInputChange}
              placeholder="Add receipt..."
              helpText="User receipts"
            />
            
            <ArrayInput
              name="messgages"
              label="Messages"
              value={formData.messgages}
              onChange={handleInputChange}
              placeholder="Add message..."
              helpText="User messages"
            />
          </div>

          {mode === 'edit' && (
            <div className="flex items-center">
              <input
                type="checkbox"
                id="emailVerified"
                checked={formData.emailVerified}
                onChange={(e) => handleInputChange(e.target.checked, 'emailVerified')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="emailVerified" className="ml-2 block text-sm text-gray-900">
                Email Verified
              </label>
            </div>
          )}
        </div>
      </div>

      {/* Form Actions */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="flex justify-between items-center">
          <div className="flex space-x-4">
            <button
              type="button"
              onClick={handleReset}
              disabled={isLoading}
              className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 disabled:opacity-50"
            >
              Reset
            </button>
            
            {onCancel && (
              <button
                type="button"
                onClick={onCancel}
                disabled={isLoading}
                className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 disabled:opacity-50"
              >
                Cancel
              </button>
            )}
          </div>
          
          <button
            type="submit"
            disabled={isLoading || !isDirty}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Saving...' : mode === 'create' ? 'Create User' : 'Update User'}
          </button>
        </div>
        
        {isDirty && (
          <p className="text-sm text-yellow-600 mt-2">
            You have unsaved changes
          </p>
        )}
      </div>
    </form>
  );
}

'use client'
import { settings } from '@/libs/siteSettings'
import Image from 'next/image'
import React from 'react'
import Link from 'next/link'

export default function BuildingsList({data}) {
    console.log('BuildingsList:',data)
  return (
    <div className='Buildings-List flex relative top-20 w-full h-[calc(100%-110px)] px-20'>
      <div className='flex w-full overflow-y-auto flex-wrap'>
        {data?.map((i,index)=>
          <div className='flex flex-col p-4 md:h-1/2 xl:h-2/3 w-1/3' key={index}>
            {/* {console.log(i)} */}
            <Link href={`/projects/${i?._id}`} className='flex flex-col w-full h-full items-center justify-center'>
              <div className='flex relative w-full flex-2/3 items-center justify-center shadow rounded-md'>
                {i?.renders?.[0]?.url && <Image src={i?.renders?.[0]?.url} alt='renders' fill/>}
              </div>
              <div className='flex flex-col w-full flex-1/3 items-start py-4 gap-1'>
                <h1 className='text-lg font-medium'>{i?.buildingTitle}</h1>
                <p className='text-sm capitalize'>{i?.buildingType}</p>
                <p className='text-sm capitalize'>{`${i?.buildingSummary?.length*i?.buildingSummary?.width}m2`}</p>
              </div>
            </Link>
          </div>
        )}
      </div>
    </div>
  )
}

import { buildings } from '@/libs/blg'
import React, { Suspense } from 'react'
import BuildPageComponent from '@/components/BuildPageComponent'
import <PERSON>roll<PERSON><PERSON>rapper from '@/components/ScrollerWrapper'
import Image from 'next/image'
import LoadingComponent from '@/components/LoadingComponent'
import ExperienceWrapper from '@/components/experience/ExperienceWrapper'

export default async function page() {
  const cssSection='sectionWrapper flex w-full h-full flex-none relative overflow-hidden'
  // const {id}=await params
  let data = null
  // try {
  //   const response = await fetch(`${settings.url}/api/products/${id}`)
    
  //   if (!response.ok) {
  //     // If the server responded with a non-2xx status code
  //     console.error(`HTTP error! status: ${response.status}`);
  //     // You might want to throw an error, redirect, or return a specific UI
  //     // For now, let's just return an empty data set or handle it
  //     return <div>Error loading product: {response.statusText}</div>;
  //   }
  //   data = await response.json();    
  // } catch (error) {
  //   console.error('Failed to fetch product data:', error);
  //   // Handle network errors or other issues during fetch
  //   return <div>Failed to load product data due to a network error.</div>;
  // }

  // if (!data) {
  //   // Handle cases where data is null after fetch attempts
  //   return <div>Product not found or no data available.</div>;
  // }

  // console.log('ProductPage:',data)
  return (
    <div className='flex w-full h-svh'>
      <BuildPageComponent data={buildings?.[0]}>
        {/* renders wrapper */}
        <section className={cssSection}>
          <ScrollerWrapper>
            {buildings?.[0]?.renders?.map((i,index)=>
              <div key={index} className='flex brightness-90 relative w-full h-full flex-none'>
                <Image priority src={i?.url} className='object-cover flex-none' alt="view" fill/>
              </div>
            )}
          </ScrollerWrapper>
        </section>
        
        {/* drawings wrapper */}
        <section className={cssSection}>
          <ScrollerWrapper>
            {buildings?.[0]?.drawings?.map((i,index)=>
              <div key={index} className='flex brightness-90 relative w-full h-full flex-none'>
                <Image priority src={i?.url} className='object-cover flex-none' alt="view" fill/>
              </div>
            )}
          </ScrollerWrapper>
        </section>

        {/* experience wrapper */}
        <section className={cssSection}>
          <Suspense fallback={<LoadingComponent/>}>
            <ExperienceWrapper data={buildings?.[0]}/>
          </Suspense>
        </section>
      </BuildPageComponent>
    </div>
  )
}

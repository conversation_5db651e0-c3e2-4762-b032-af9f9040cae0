// src/app/admin/users/[id]/edit/page.jsx
// Edit user page

"use client";

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import UserForm from '../../../../../components/forms/UserForm';

export default function EditUserPage({ params }) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [error, setError] = useState('');

  // Redirect if not authenticated
  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!session) {
    router.push('/auth/signin');
    return null;
  }

  // Check permissions
  const isAdmin = session.user.role === 'admin';
  const isSelfEdit = session.user.id === params.id;

  if (!isAdmin && !isSelfEdit) {
    router.push('/not-authorized');
    return null;
  }

  // Fetch user data
  useEffect(() => {
    const fetchUser = async () => {
      try {
        setIsLoadingData(true);
        const response = await fetch(`/api/users/${params.id}`);
        
        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('User not found');
          }
          throw new Error('Failed to fetch user data');
        }

        const data = await response.json();
        setUser(data.user);
        setError('');
      } catch (err) {
        setError(err.message);
      } finally {
        setIsLoadingData(false);
      }
    };

    if (params.id) {
      fetchUser();
    }
  }, [params.id]);

  const handleSubmit = async (formData) => {
    setIsLoading(true);
    setError('');

    try {
      const response = await fetch(`/api/users/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update user');
      }

      const result = await response.json();
      
      // Redirect to the user detail page with success message
      if (isAdmin) {
        router.push(`/admin/users/${params.id}?updated=true`);
      } else {
        router.push('/dashboard?updated=true');
      }
      
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    if (confirm('Are you sure you want to cancel? All unsaved changes will be lost.')) {
      if (isAdmin) {
        router.push(`/admin/users/${params.id}`);
      } else {
        router.push('/dashboard');
      }
    }
  };

  // Loading state
  if (isLoadingData) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-lg shadow-sm border p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-600 mt-2">Loading user data...</p>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error && !user) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-red-50 border border-red-200 rounded-md p-6 text-center">
            <div className="flex justify-center">
              <svg className="h-12 w-12 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-red-800 mt-2">Error Loading User</h3>
            <p className="text-red-700 mt-1">{error}</p>
            <div className="mt-4">
              <Link
                href={isAdmin ? "/admin/users" : "/dashboard"}
                className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
              >
                {isAdmin ? 'Back to Users' : 'Back to Dashboard'}
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4">
            <Link
              href={isAdmin ? `/admin/users/${params.id}` : '/dashboard'}
              className="text-blue-600 hover:text-blue-800 flex items-center"
            >
              <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              {isAdmin ? 'Back to User' : 'Back to Dashboard'}
            </Link>
            {isAdmin && (
              <>
                <span className="text-gray-300">|</span>
                <Link
                  href="/admin/users"
                  className="text-blue-600 hover:text-blue-800"
                >
                  All Users
                </Link>
              </>
            )}
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mt-4">
            {isSelfEdit ? 'Edit Your Profile' : `Edit User: ${user?.name || user?.email}`}
          </h1>
          <p className="text-gray-600 mt-2">
            {isSelfEdit ? 'Update your account information' : 'Update user account information'}
          </p>
        </div>

        {/* Error Message */}
        {error && user && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error updating user</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* User Form */}
        {user && (
          <UserForm
            mode="edit"
            initialData={user}
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            isLoading={isLoading}
            canEditRole={isAdmin && !isSelfEdit}
          />
        )}
      </div>
    </div>
  );
}

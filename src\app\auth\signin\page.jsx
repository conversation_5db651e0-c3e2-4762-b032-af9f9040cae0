// app/auth/signin/page.js
// This page provides custom sign-in forms for credentials, Google, Facebook, and Magic Link.

"use client"; // This component runs on the client side

import { useState, useEffect } from "react";
import { signIn, getSession } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";

export default function SignInPage() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [showLinkAccount, setShowLinkAccount] = useState(false);
  const [linkingEmail, setLinkingEmail] = useState("");
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get("callbackUrl") || "/dashboard"; // Redirect after login

  console.log("Sign-in page loaded with callbackUrl:", callbackUrl);
  const errorParam = searchParams.get("error");
  const emailParam = searchParams.get("email");

  // Handle OAuth account linking error
  useEffect(() => {
    if (errorParam === "OAuthAccountNotLinked" && emailParam) {
      setError(`An account with ${emailParam} already exists. Please sign in with your existing method to link your Google account.`);
      setLinkingEmail(emailParam);
      setShowLinkAccount(true);
      setEmail(emailParam);
    }
  }, [errorParam, emailParam]);

  const handleCredentialsSignIn = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    const result = await signIn("credentials", {
      redirect: true, // Let NextAuth handle the redirect
      email,
      password,
      callbackUrl: callbackUrl
    });

    setLoading(false);

    // Note: When redirect: true, NextAuth handles the redirect automatically
    // This code will only run if there's an error or if redirect fails
    if (result?.error) {
      setError(result.error);
      console.error("Credentials sign-in error:", result.error);
    } else {
      console.log("Credentials sign-in successful - NextAuth should redirect automatically");

      // If this was triggered by OAuth account linking, offer to link the account
      if (showLinkAccount && linkingEmail) {
        setShowLinkAccount(false);
        setError("");
        // Show success message and link account option
        const linkAccount = confirm("Sign-in successful! Would you like to link your Google account now?");
        if (linkAccount) {
          await handleLinkGoogleAccount();
          return;
        }
      }

      // Fallback redirect if NextAuth doesn't redirect automatically
      console.log("Fallback redirect to:", callbackUrl);
      setTimeout(() => {
        window.location.href = callbackUrl;
      }, 1000);
    }
  };

  const handleLinkGoogleAccount = async () => {
    try {
      setLoading(true);
      const result = await signIn("google", {
        redirect: false,
        callbackUrl: callbackUrl
      });

      if (result.error) {
        setError("Failed to link Google account: " + result.error);
      } else {
        alert("Google account linked successfully!");
        window.location.href = callbackUrl;
      }
    } catch (error) {
      setError("Failed to link Google account");
      console.error("Account linking error:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleMagicLinkSignIn = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    if (!email) {
      setError("Please enter your email for magic link.");
      setLoading(false);
      return;
    }

    const result = await signIn("email", {
      redirect: false,
      email,
      callbackUrl: callbackUrl, // Specify where to redirect after email verification
    });

    setLoading(false);

    if (result.error) {
      setError(result.error);
      console.error("Magic link sign-in error:", result.error);
    } else {
      // NextAuth will handle the redirect to verifyRequest page.
      // You might want to show a message to the user here.
      alert("A sign-in link has been sent to your email address.");
      router.push("/auth/verify-request"); // Redirect to a page informing user to check email
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 p-4">
      <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
        <h1 className="text-3xl font-bold text-center mb-6 text-gray-800">Sign In</h1>

        {error && (
          <div className={`border px-4 py-3 rounded relative mb-4 ${
            showLinkAccount
              ? "bg-yellow-100 border-yellow-400 text-yellow-700"
              : "bg-red-100 border-red-400 text-red-700"
          }`} role="alert">
            <strong className="font-bold">
              {showLinkAccount ? "Account Linking Required! " : "Error! "}
            </strong>
            <span className="block sm:inline">{error}</span>
          </div>
        )}

        {showLinkAccount && (
          <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded relative mb-4">
            <strong className="font-bold">Next Steps:</strong>
            <ol className="list-decimal list-inside mt-2 text-sm">
              <li>Sign in with your existing email and password below</li>
              <li>After successful sign-in, you'll be offered to link your Google account</li>
            </ol>
          </div>
        )}

        {/* Credentials Sign-in Form */}
        <form onSubmit={handleCredentialsSignIn} className="mb-6">
          <div className="mb-4">
            <label htmlFor="email" className="block text-gray-700 text-sm font-bold mb-2">
              Email
            </label>
            <input
              type="email"
              id="email"
              className="shadow appearance-none border rounded-lg w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>
          <div className="mb-6">
            <label htmlFor="password" className="block text-gray-700 text-sm font-bold mb-2">
              Password
            </label>
            <input
              type="password"
              id="password"
              className="shadow appearance-none border rounded-lg w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </div>
          <button
            type="submit"
            className="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg focus:outline-none focus:shadow-outline transition duration-200"
            disabled={loading}
          >
            {loading ? "Signing In..." : "Sign In with Email & Password"}
          </button>
        </form>

        <div className="text-center text-gray-600 mb-6">Or continue with</div>

        {/* Social and Magic Link Buttons */}
        <div className="space-y-4">
          <button
            onClick={() => signIn("google", { callbackUrl })}
            className="w-full flex items-center justify-center bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg focus:outline-none focus:shadow-outline transition duration-200"
            disabled={loading}
          >
            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12.0003 4.75C14.0723 4.75 15.8313 5.448 17.1553 6.726L20.0633 3.818C18.0053 1.918 15.2343 0.75 12.0003 0.75C7.30332 0.75 3.20332 3.418 1.29332 7.375L5.27532 10.362C6.15732 7.643 8.82832 5.75 12.0003 5.75C12.0003 5.75 12.0003 4.75 12.0003 4.75Z" fill="#EA4335" />
              <path d="M23.25 12.25C23.25 11.488 23.181 10.743 23.044 10.019H12V14.5H18.471C18.188 15.932 17.301 17.151 16.035 18.008L19.091 20.301C20.915 18.665 22.083 16.326 22.75 13.844C23.003 13.064 23.181 12.25 23.25 12.25Z" fill="#4285F4" />
              <path d="M5.27532 14.362L1.29332 17.35C3.20332 21.307 7.30332 23.975 12.0003 23.975C15.2343 23.975 18.0053 22.807 20.0633 20.907L17.1553 18.00C15.8313 19.278 14.0723 19.975 12.0003 19.975C8.82832 19.975 6.15732 18.082 5.27532 15.363L5.27532 14.362Z" fill="#FBBC05" />
              <path d="M12.0003 19.975C8.82832 19.975 6.15732 18.082 5.27532 15.363L5.27532 14.362L1.29332 17.35C1.65032 18.093 2.05932 18.802 2.51832 19.479L5.27532 14.362L5.27532 14.362Z" fill="#34A853" />
            </svg>
            Sign In with Google
          </button>
          <button
            onClick={() => signIn("facebook", { callbackUrl })}
            className="w-full flex items-center justify-center bg-blue-800 hover:bg-blue-900 text-white font-bold py-2 px-4 rounded-lg focus:outline-none focus:shadow-outline transition duration-200"
            disabled={loading}
          >
            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.0202 5.67923 21.1216 10.5129 21.8741V14.1537H7.47937V10.5129H10.5129V7.89271C10.5129 4.88796 12.3879 3.23725 15.1977 3.23725C16.5398 3.23725 17.7027 3.33642 18.0538 3.38531V6.52984H16.1415C14.6193 6.52984 14.3314 7.27984 14.3314 8.28314V10.5129H17.8727L17.3147 14.1537H14.3314V21.8741C19.3208 21.1216 23 17.0202 23 12C23 6.47715 18.5228 2 13 2C7.47715 2 3 6.47715 3 12C3 17.5228 7.47715 22 13 22C17.5228 22 21.1216 18.3208 21.8741 13.4871H23V12H22Z" />
            </svg>
            Sign In with Facebook
          </button>
          {/* <button
            onClick={handleMagicLinkSignIn}
            className="w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded-lg focus:outline-none focus:shadow-outline transition duration-200"
            disabled={loading}
          >
            {loading ? "Sending Link..." : "Sign In with Magic Link"}
          </button> */}
        </div>

        <p className="text-center text-gray-600 text-sm mt-6">
          Don't have an account?{" "}
          <a href="/auth/register" className="text-blue-500 hover:underline">
            Register here
          </a>
        </p>
      </div>
    </div>
  );
}

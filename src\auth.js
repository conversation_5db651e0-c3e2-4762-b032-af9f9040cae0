// src/auth.js
import NextAuth from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import EmailProvider from "next-auth/providers/email";
import { MongoDBAdapter } from "@auth/mongodb-adapter";
// FIX: Corrected import path for mongodb-client-promise.js relative to src/auth.js
import clientPromise from "./libs/mongoDb/mongodb-client-promise";
import bcrypt from "bcryptjs";
// FIX: Corrected import path for nodemailer.js relative to src/auth.js
// Based on image: src/libs/nodemailer/nodemailer.js
import { sendVerificationRequest } from "./libs/nodemailer/nodemailer";
import { authConfig } from "./auth.config"; // auth.config.js is sibling to auth.js

const NextAuthInstance = NextAuth({
  ...authConfig,
  adapter: MongoDBAdapter(clientPromise),
  providers: [
    ...authConfig.providers,
    EmailProvider({
      server: {
        host: process.env.EMAIL_SERVER_HOST,
        port: process.env.EMAIL_SERVER_PORT,
        auth: {
          user: process.env.EMAIL_SERVER_USER,
          pass: process.env.EMAIL_SERVER_PASSWORD,
        },
      },
      from: process.env.EMAIL_FROM,
      sendVerificationRequest,
    }),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        const client = await clientPromise;
        const db = client.db();
        const usersCollection = db.collection("users");

        const user = await usersCollection.findOne({ email: credentials.email });

        if (!user) {
          console.log("No user found with this email.");
          return null;
        }

        const isValidPassword = await bcrypt.compare(credentials.password, user.password);

        if (!isValidPassword) {
          console.log("Invalid password.");
          return null;
        }

        return {
          id: user._id.toString(),
          email: user.email,
          name: user.name || user.email,
          role: user.role || "user",
        };
      },
    }),
  ],
});

export const { handlers, auth, signIn, signOut } = NextAuthInstance;

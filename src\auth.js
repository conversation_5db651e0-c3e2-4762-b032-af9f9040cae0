// src/auth.js
import NextAuth from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import EmailProvider from "next-auth/providers/email";
import { MongoDBAdapter } from "@auth/mongodb-adapter";
// FIX: Corrected import path for mongodb-client-promise.js relative to src/auth.js
import clientPromise from "./libs/mongoDb/mongodb-client-promise";
import bcrypt from "bcryptjs";
// FIX: Corrected import path for nodemailer.js relative to src/auth.js
// Based on image: src/libs/nodemailer/nodemailer.js
import { sendVerificationRequest } from "./libs/nodemailer/nodemailer";
import { authConfig } from "./auth.config"; // auth.config.js is sibling to auth.js

const NextAuthInstance = NextAuth({
  ...authConfig,
  adapter: MongoDBAdapter(clientPromise),
  session: {
    strategy: "database", // Override to use database sessions when adapter is available
  },
  callbacks: {
    // Override callbacks for database sessions
    async session({ session, user }) {
      // With database sessions, user data comes from the database
      if (user) {
        session.user.id = user.id;
        session.user.role = user.role || "user";
      }
      return session;
    },
    // Keep the redirect callback from authConfig
    ...authConfig.callbacks,
  },
  providers: [
    ...authConfig.providers,
    EmailProvider({
      server: {
        host: process.env.EMAIL_SERVER_HOST,
        port: process.env.EMAIL_SERVER_PORT,
        auth: {
          user: process.env.EMAIL_SERVER_USER,
          pass: process.env.EMAIL_SERVER_PASSWORD,
        },
      },
      from: process.env.EMAIL_FROM,
      sendVerificationRequest,
    }),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        try {
          // Validate input credentials
          if (!credentials?.email || !credentials?.password) {
            console.log("Missing email or password in credentials");
            return null;
          }

          const client = await clientPromise;
          const db = client.db();
          const usersCollection = db.collection("users");

          const user = await usersCollection.findOne({ email: credentials.email });

          if (!user) {
            console.log("No user found with this email:", credentials.email);
            return null;
          }

          // Check if user has a password (some users might be OAuth-only)
          if (!user.password) {
            console.log("User exists but has no password (OAuth-only account):", credentials.email);
            return null;
          }

          // Validate password
          const isValidPassword = await bcrypt.compare(credentials.password, user.password);

          if (!isValidPassword) {
            console.log("Invalid password for user:", credentials.email);
            return null;
          }

          console.log("Successful credentials authentication for:", credentials.email);
          return {
            id: user._id.toString(),
            email: user.email,
            name: user.name || user.email,
            role: user.role || "user",
          };
        } catch (error) {
          console.error("Error in credentials authorization:", error);
          return null;
        }
      },
    }),
  ],
});

export const { handlers, auth, signIn, signOut } = NextAuthInstance;

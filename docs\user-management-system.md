# User Management System Documentation

## Overview

A comprehensive user management system with OAuth account linking functionality, admin controls, and secure user operations.

## Features Implemented

### ✅ **1. OAuth Account Linking Error Handling**

#### **Error Detection & User Flow**
- **Error Detection**: Automatically detects `OAuthAccountNotLinked` error when users arrive at `/auth/signin?error=OAuthAccountNotLinked&email=<EMAIL>`
- **Clear Messaging**: Displays user-friendly message explaining the situation
- **Guided Flow**: Provides step-by-step instructions for account linking
- **Secure Linking**: After successful credentials sign-in, offers Google account linking option

#### **Implementation Details**
- **Modified Sign-in Page**: Enhanced `/auth/signin/page.jsx` with error handling
- **Visual Indicators**: Different colored alerts for linking vs. error states
- **Account Linking API**: `/api/auth/link-account` endpoint for secure account merging
- **Automatic Detection**: useEffect hook monitors URL parameters for errors

### ✅ **2. Admin User Setup**
- **Admin User Created**: `<EMAIL>` added with admin role
- **Default Credentials**: Password set to `AdminPassword123!` (should be changed)
- **Email Verified**: Admin account marked as email verified
- **Database Integration**: Properly stored in MongoDB with all required fields

### ✅ **3. Complete User Management System**

#### **API Endpoints**
- **GET** `/api/users` - List users with pagination, search, and filtering (Admin only)
- **GET** `/api/users/[id]` - Get single user (Admin or self)
- **POST** `/api/users` - Create new user (Admin only)
- **PUT** `/api/users/[id]` - Update user (Admin or self, with restrictions)
- **DELETE** `/api/users/[id]` - Delete user (Admin only, cannot delete self)

#### **User Interface Components**

##### **User List Dashboard** (`/admin/users`)
- **Paginated Table**: Displays all users with avatar, name, email, role, verification status
- **Search Functionality**: Search by name or email
- **Role Filtering**: Filter by user role (user/admin)
- **Action Buttons**: View, Edit, Delete for each user
- **Responsive Design**: Works on mobile and desktop
- **Admin Protection**: Only accessible to admin users

##### **Create User Page** (`/admin/users/create`)
- **Comprehensive Form**: All fields from User model
- **Password Requirements**: Minimum 6 characters with confirmation
- **Role Assignment**: Admin can assign user or admin role
- **Array Fields**: Support for invites, invoices, receipts, messages
- **Validation**: Real-time form validation with error messages

##### **Edit User Page** (`/admin/users/[id]/edit`)
- **Pre-populated Form**: Existing user data loaded
- **Permission Checks**: Admin can edit all users, users can edit themselves
- **Role Restrictions**: Users cannot change their own role, admins cannot remove their own admin status
- **Password Updates**: Optional password changes (leave blank to keep current)
- **Self-Edit Support**: Users can edit their own profiles

##### **View User Page** (`/admin/users/[id]`)
- **Detailed Display**: Complete user information with organized layout
- **Profile Statistics**: Count of invites, invoices, receipts, messages
- **Account Status**: Email verification, creation date, last update
- **Linked Accounts**: Shows connected OAuth providers
- **Action Buttons**: Edit and Delete (with appropriate permissions)

### ✅ **4. Form Components Integration**
- **UserForm Component**: Modular form using existing form components
- **TextInput**: Name, email, password fields with validation
- **SelectInput**: Role selection dropdown
- **ArrayInput**: Support for invites, invoices, receipts, messages arrays
- **Validation**: Comprehensive client-side and server-side validation

## Technical Implementation

### **Database Schema (User Model)**
```javascript
{
  name: String,           // User's full name
  email: String,          // Email address (unique)
  password: String,       // Hashed password (optional for OAuth users)
  role: String,           // 'user' or 'admin'
  invites: [String],      // Array of invitations
  invoices: [String],     // Array of invoices
  recipets: [String],     // Array of receipts (note: typo preserved from original)
  messgages: [String],    // Array of messages (note: typo preserved from original)
  image: String,          // Profile image URL
  emailVerified: Date,    // Email verification timestamp
  createdAt: Date,        // Account creation date
  updatedAt: Date         // Last update timestamp
}
```

### **Security Features**
- **Authentication Required**: All endpoints require valid session
- **Role-based Access Control**: Admin-only operations clearly defined
- **Self-Protection**: Users cannot delete themselves or remove their own admin status
- **Password Hashing**: bcryptjs with 10 salt rounds
- **Input Validation**: Comprehensive validation on both client and server
- **Email Uniqueness**: Prevents duplicate email addresses

### **OAuth Account Linking Process**
1. **Error Detection**: User tries to sign in with Google but email exists with different provider
2. **Error Display**: Clear message shown with linking instructions
3. **Credentials Sign-in**: User signs in with existing method
4. **Link Offer**: System offers to link Google account
5. **Secure Linking**: API endpoint safely merges account data
6. **Verification**: Email marked as verified for Google links

### **Permission Matrix**
| Action | Admin | User (Self) | User (Other) |
|--------|-------|-------------|--------------|
| View Users List | ✅ | ❌ | ❌ |
| View User Details | ✅ | ✅ | ❌ |
| Create User | ✅ | ❌ | ❌ |
| Edit User | ✅ | ✅ | ❌ |
| Delete User | ✅ | ❌ | ❌ |
| Change Role | ✅ | ❌ | ❌ |
| Delete Self | ❌ | ❌ | ❌ |

## Usage Guide

### **Admin Setup**
1. **Login as Admin**: Use `<EMAIL>` with password `AdminPassword123!`
2. **Change Password**: Immediately change the default password
3. **Access User Management**: Navigate to `/admin/users`

### **Managing Users**
1. **View All Users**: Go to `/admin/users` to see paginated user list
2. **Search Users**: Use search bar to find specific users
3. **Filter by Role**: Use dropdown to filter by user role
4. **Create New User**: Click "Create New User" button
5. **Edit User**: Click "Edit" button next to any user
6. **Delete User**: Click "Delete" button (cannot delete yourself)

### **OAuth Account Linking**
1. **Error Occurs**: User sees linking error message
2. **Follow Instructions**: Sign in with existing credentials
3. **Accept Linking**: Confirm when prompted to link Google account
4. **Verification**: Account is linked and email verified

### **User Self-Management**
- Users can edit their own profiles via `/admin/users/[their-id]/edit`
- Users cannot change their own role
- Users cannot delete their own accounts

## File Structure
```
src/
├── app/
│   ├── admin/users/
│   │   ├── page.jsx                    # User list dashboard
│   │   ├── create/page.jsx             # Create user page
│   │   └── [id]/
│   │       ├── page.jsx                # View user page
│   │       └── edit/page.jsx           # Edit user page
│   ├── api/
│   │   ├── users/
│   │   │   ├── route.js                # User CRUD operations
│   │   │   └── [id]/route.js           # Individual user operations
│   │   └── auth/link-account/route.js  # Account linking API
│   ├── auth/signin/page.jsx            # Enhanced sign-in with linking
│   └── not-authorized/page.jsx         # Access denied page
├── components/forms/
│   └── UserForm.jsx                    # User form component
└── scripts/
    └── setup-admin-user.js             # Admin user setup script
```

## Dependencies Added
- `bcryptjs` - Password hashing for secure credential storage

## Environment Variables
No additional environment variables required - uses existing MongoDB and NextAuth configuration.

## Testing Checklist

### **OAuth Account Linking**
- [ ] Try signing in with Google using email that exists with credentials
- [ ] Verify error message displays correctly
- [ ] Sign in with credentials and confirm linking offer
- [ ] Verify Google account is linked successfully

### **User Management (Admin)**
- [ ] Access user list at `/admin/users`
- [ ] Search and filter functionality
- [ ] Create new user with all fields
- [ ] Edit existing user information
- [ ] Delete user (not yourself)
- [ ] Verify pagination works with many users

### **User Self-Management**
- [ ] User can edit their own profile
- [ ] User cannot change their own role
- [ ] User cannot access other users' data
- [ ] User cannot access admin-only pages

### **Security**
- [ ] Non-admin users redirected from admin pages
- [ ] Cannot delete own account
- [ ] Cannot remove own admin status
- [ ] Password hashing works correctly
- [ ] Email uniqueness enforced

## Git Commit Summary
```
feat: implement comprehensive user management system with OAuth linking

- Add OAuth account linking error handling in sign-in page
- Create account linking API endpoint for secure account merging
- <NAME_EMAIL> as admin user with proper role
- Implement complete user CRUD API with role-based permissions
- Add user management dashboard with search, filter, and pagination
- Create user form component with validation and array field support
- Build create, edit, and view user pages with responsive design
- Add permission checks preventing self-deletion and role manipulation
- Integrate bcryptjs for secure password hashing
- Support user self-editing with appropriate restrictions

Features: OAuth linking, User CRUD, Admin dashboard, Role-based access, Security
```

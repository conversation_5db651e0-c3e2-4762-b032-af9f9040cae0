// src/app/api/auth/forgot-password/route.js
// This API route handles the request to initiate a password reset.

import { NextResponse } from "next/server";
// FIX: Corrected import path for mongodb-client-promise.js
// Path from route.js to src/libs/mongoDb/mongodb-client-promise.js:
// route.js -> forgot-password -> auth -> api -> app -> src -> libs/mongoDb/mongodb-client-promise.js
import clientPromise from "../../../../../libs/mongoDb/mongodb-client-promise";
// FIX: Corrected import path for nodemailer.js
// Path from route.js to src/lib/nodemailer.js:
// route.js -> forgot-password -> auth -> api -> app -> src -> lib/nodemailer.js
import { sendPasswordResetEmail } from "../../../../../libs/nodemailer/nodemailer";
import crypto from "crypto";

export async function POST(request) {
  try {
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json(
        { message: "Email is required." },
        { status: 400 }
      );
    }

    const client = await clientPromise;
    const db = client.db();
    const usersCollection = db.collection("users");

    const user = await usersCollection.findOne({ email });

    if (!user) {
      console.log(`Password reset requested for non-existent email: ${email}`);
      return NextResponse.json(
        { message: "If an account with that email exists, a password reset link has been sent." },
        { status: 200 }
      );
    }

    const resetToken = crypto.randomBytes(32).toString("hex");
    const resetTokenExpiry = Date.now() + 3600000; // 1 hour in milliseconds

    await usersCollection.updateOne(
      { _id: user._id },
      { $set: { resetToken, resetTokenExpiry } }
    );

    const resetLink = `${process.env.AUTH_URL}/auth/reset-password/${resetToken}`;

    await sendPasswordResetEmail({ email: user.email, resetLink });

    return NextResponse.json(
      { message: "If an account with that email exists, a password reset link has been sent." },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error in forgot password API:", error);
    return NextResponse.json(
      { message: "An internal server error occurred while processing your request." },
      { status: 500 }
    );
  }
}

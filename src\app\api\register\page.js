// src/app/api/register/route.js
// This API route handles the registration of new users with email and password.

import { NextResponse } from "next/server";
// FIX: Corrected import path for mongodb-client-promise.js
// Path from route.js to src/libs/mongoDb/mongodb-client-promise.js:
// route.js (in register) -> api -> app -> src -> libs/mongoDb/mongodb-client-promise.js
// This means going up 3 levels to 'src', then down into 'libs/mongoDb/'
import clientPromise from "../../../libs/mongoDb/mongodb-client-promise"; // Corrected path
import bcrypt from "bcryptjs";

export async function POST(request) {
  try {
    const { name, email, password } = await request.json();

    if (!email || !password) {
      return NextResponse.json(
        { message: "Email and password are required." },
        { status: 400 }
      );
    }

    const client = await clientPromise;
    const db = client.db();
    const usersCollection = db.collection("users");

    // Check if user already exists
    const existingUser = await usersCollection.findOne({ email });
    if (existingUser) {
      return NextResponse.json(
        { message: "User with this email already exists." },
        { status: 409 }
      );
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 10); // 10 is the salt rounds

    // Create a new user document
    const newUser = {
      name: name || email, // Use name if provided, otherwise email
      email,
      password: hashedPassword,
      role: "user", // Assign a default role
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Insert the new user into the database
    const result = await usersCollection.insertOne(newUser);

    if (result.acknowledged) {
      return NextResponse.json(
        { message: "User registered successfully." },
        { status: 201 }
      );
    } else {
      throw new Error("Failed to insert user.");
    }
  } catch (error) {
    console.error("Registration API error:", error);
    return NextResponse.json(
      { message: "Internal server error." },
      { status: 500 }
    );
  }
}

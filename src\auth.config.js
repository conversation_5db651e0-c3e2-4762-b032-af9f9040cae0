// auth.config.js
// This file defines the base NextAuth.js configuration.
// It explicitly avoids importing Node.js-specific modules like 'nodemailer'
// so it can be safely used in the Edge Runtime (e.g., by middleware).

import <PERSON> from "next-auth/providers/google";
import Facebook from "next-auth/providers/facebook";
// Note: Credentials and Email providers are configured in auth.js,
// as they require Node.js-specific modules (bcryptjs, nodemailer).

/** @type {import("next-auth").AuthConfig} */
export const authConfig = {
  // Use JWT for session management
  session: {
    strategy: "jwt",
  },
  providers: [
    // Google OAuth Provider
    Google({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    }),
    // Facebook OAuth Provider
    Facebook({
      clientId: process.env.FACEBOOK_CLIENT_ID,
      clientSecret: process.env.FACEBOOK_CLIENT_SECRET,
    }),
    // Credentials and Email providers are added in auth.js,
    // where Node.js environment is guaranteed.
  ],
  callbacks: {
    // JWT callback: This callback is called whenever a JWT is created (on sign-in)
    // or updated (e.g., on session refresh).
    async jwt({ token, user, account, profile }) {
      // If a user object is provided (during sign-in), add custom properties to the token
      if (user) {
        token.id = user.id;
        token.role = user.role; // Add the user's role to the token
      }
      return token;
    },
    // Session callback: This callback is called whenever a session is checked.
    // It allows you to expose custom properties from the JWT to the session.
    async session({ session, token }) {
      // Expose user ID and role from the token to the session object
      if (token) {
        session.user.id = token.id;
        session.user.role = token.role;
      }
      return session;
    },
    // Redirect callback: Customize where the user is redirected after sign-in/sign-out
    async redirect({ url, baseUrl }) {
      // Allows relative callback URLs
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      // Allows callback URLs on the same origin
      else if (new URL(url).origin === baseUrl) return url;
      return baseUrl; // Default redirect to base URL
    },
  },
  // Custom pages for authentication flow
  pages: {
    signIn: "/auth/signin", // Custom sign-in page
    error: "/auth/error", // Custom error page
    verifyRequest: "/auth/verify-request", // Custom page for magic link verification
  },
  // Debug mode for more verbose logging in development
  debug: process.env.NODE_ENV === "development",
};

'use client'
import ExperienceGLTFLoader from './ExperienceGLTFLoader'
import { Environment } from '@react-three/drei'

export default function ExperienceAR({data}) {
  // console.log('ExperienceModel:',data?.position?.split(',').map(i=>Number(i)))
  return (
    <>
      <group 
        name="ExperienceAR"
        position={data?.position?.split(',').map(i=>Number(i))}
      >
        {data?.modelsFiles?.map((model,index)=>
          <ExperienceGLTFLoader key={index} path={model}/>
        )}
        {data?.hideLevel?.map((model,index)=>
          <ExperienceGLTFLoader key={index} path={model}/>
        )}
        {data?.supportFiles?.map((model,index)=>
          <ExperienceGLTFLoader key={index} path={model}/>
        )}
        {data?.roomSnaps?.map((model,index)=>
          <ExperienceGLTFLoader key={index} path={model}/>
        )}
      </group>
      <Environment preset="city"/>
    </>
  )
}

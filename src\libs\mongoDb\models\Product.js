import mongoose from 'mongoose';
const { Schema } = mongoose;

const productSchema = new Schema({
    projectTitle:{type:String,required:true,unique:true},
    price:{type:String,required:true},
    buildingTitle:{type:String,required:true},
    buildingType:{type:String,required:true},
    desc:{type:String,required:true},
    position:{type:String,required:true},
    arPosition:{type:String,required:true},
    minDistance:{type:String,required:true},
    maxDistance:{type:String,required:true},
    buildingRole:{type:String,default:'exhibit'},
    features:{type:String,required:true},
    outroSection:{type:String,required:true},

    buildingSummary:{type:Object,required:true},

    buildingHighlights:{type:Array,required:true},
    tags:{type:Array},
    color:{type:Array},

    renders:{type:Array,required:true},
    drawings:{type:Array,required:true},

    modelsFiles:{type:Array,required:true},
    hideLevel:{type:Array,required:true},
    supportFiles:{type:Array,required:true},
    _360sImages:{type:Array,required:true},
    roomSnaps:{type:Array,required:true},
    presentationDrawings:{type:Array,required:true},
    constructionDrawingsPdf:{type:Array,required:true},
    constructionDrawingsDwg:{type:Array,required:true},
    collections:{type:Array,required:true},
    
    coments:{type:Array},
    likes:{type:Array},
    rating:{type:String},
    thumbnail:{type:String},
    warrantyInformation:{type:String},
    shippingInformation:{type:String},
    returnPolicy:{type:String},
    reviews:{type:Object},
},{timestamps:true});

export const Product = mongoose.models.Product||mongoose.model('Product', productSchema)
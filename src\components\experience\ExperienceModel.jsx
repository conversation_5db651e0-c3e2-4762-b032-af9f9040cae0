'use client'
import React, {useEffect} from 'react'
import ExperienceGLTFLoader from './ExperienceGLTFLoader'
import { Environment } from '@react-three/drei'
import { useThree } from '@react-three/fiber'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'

export default function ExperienceModel({data}) {
  const {experienceState,experienceDispatch}=useExperienceContext()
  const {scene}=useThree() 

  const handleLevelToHde = () => {
    if(experienceState?.levelToHide){
      // console.log(experienceState?.levelToHide)
      scene.getObjectByName(experienceState?.levelToHide?.name).traverse((child) => {
        if (child.isMesh) {
          child.visible = experienceState?.levelToHide?.toggleLevelState;
        }
      });
    }
  }

  useEffect(() => {
    handleLevelToHde()
  }, [experienceState?.levelToHide?.toggleLevelState])
  
  return (
    <>
      <group 
        name="ExperienceModel"
        position={data?.position?.split(',').map(i=>Number(i))}
      >
        {data?.modelsFiles?.map((model,index)=>
          <ExperienceGLTFLoader key={index} path={model}/>
        )}
        {data?.hideLevel?.map((model,index)=>
          <ExperienceGLTFLoader key={index} path={model}/>
        )}
        {data?.supportFiles?.map((model,index)=>
          <ExperienceGLTFLoader key={index} path={model}/>
        )}
        <group name="roomSnaps">
          {data?.roomSnaps?.map((model,index)=>
            <ExperienceGLTFLoader key={index} path={model}/>
          )}
        </group>
      </group>
      <Environment preset="city"/>
    </>
  )
}

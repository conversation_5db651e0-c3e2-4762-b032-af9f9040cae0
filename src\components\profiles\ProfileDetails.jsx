// src/components/ProfileDetails/ProfileDetails.jsx
import React, { useState } from 'react';
import ProfileSection from './ProfileSection';

const ProfileDetails = ({ user, onSave }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState(user);
  const [inviteList, setInviteList] = useState([]);
  const [inputDtata, setInputDtata] = useState();
  const [showError, setShowError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  if (!user) {
    return <ProfileSection title="Details"><div className="text-gray-600">Loading details...</div></ProfileSection>;
  }

  const addToList = () => {
    console.log('addToList:',inputDtata)
    const newValue = inputDtata;
    console.log(newValue)

    if (!inputDtata) {
      setShowError(true);
      setErrorMessage('Email is required');
      return;
    }

    if (!inviteList.includes(inputDtata)) {
      setInviteList(prev => [...prev, inputDtata]);
    } else {
      // Optionally show a message that email already exists
      console.log('Email already exists in invite list');
      setShowError(true);
      setErrorMessage('Email already exists in invite list');
    }
  }

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prevData => ({ ...prevData, [name]: value }));
  };

  const handleSave = () => {
    console.log('Saving profile data:', formData);
    onSave(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(user);
    setIsEditing(false);
  };

  // console.log('ProfileDetails:',inputDtata)
  return (
    <ProfileSection title="Personal Details">
      {isEditing ? (
        <div className="space-y-4">
          <div className="flex flex-col">
            <label htmlFor="firstName" className="block text-gray-700 font-bold mb-1">First Name:</label>
            <input
              type="text"
              id="firstName"
              name="firstName"
              value={formData.firstName || ''}
              onChange={handleChange}
              className="p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div className="flex flex-col">
            <label htmlFor="lastName" className="block text-gray-700 font-bold mb-1">Last Name:</label>
            <input
              type="text"
              id="lastName"
              name="lastName"
              value={formData.lastName || ''}
              onChange={handleChange}
              className="p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div className="flex flex-col">
            <label htmlFor="email" className="block text-gray-700 font-bold mb-1">Email:</label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email || ''}
              onChange={handleChange}
              className="p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div className="flex flex-col">
            <label htmlFor="location" className="block text-gray-700 font-bold mb-1">Site Location:</label>
            <input
              type="text"
              id="location"
              name="location"
              value={formData.location || ''}
              onChange={handleChange}
              className="p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div className="flex flex-col gap-2">
            <label htmlFor="email" className="block text-gray-700 font-bold mb-1">Add Email Invite:</label>
            <div className="flex items-center gap-2">
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email || ''}
                onChange={(e)=>setInputDtata(e.target.value)}
                className="p-2 border border-gray-300 rounded-md focus:outline-none w-full focus:ring-2 focus:ring-blue-500"
              />
              <button onClick={addToList} className='flex w-fit px-8 items-center justify-center min-h-10 rounded-md bg-slate-800 cursor-pointer outline-none hover:bg-slate-700 duration-300 ease-linear text-sm capitalize text-white'>Add</button>
            </div>
            <div className='flex flex-wrap gap-2'>
              {inviteList.map((i,index)=>
                <div key={index} className="inline-flex items-center px-3 py-1 flex-wrap p-2 max-h-32 rounded-full text-sm bg-blue-100 text-blue-800 border border-blue-200">
                  {i}
                </div>
              )}
            </div>
          </div>
          <div>

          </div>
          <div className="flex justify-end space-x-3 mt-5">
            <button
              className="bg-green-600 text-white px-5 py-2 rounded-md font-semibold hover:bg-green-700 transition-colors"
              onClick={handleSave}
            >
              Save
            </button>
            <button
              className="bg-gray-500 text-white px-5 py-2 rounded-md font-semibold hover:bg-gray-600 transition-colors"
              onClick={handleCancel}
            >
              Cancel
            </button>
          </div>
        </div>
      ) : (
        <div className="space-y-2">
          <p className="text-gray-700"><strong className="text-gray-800">Email:</strong> {user.email}</p>
          <p className="text-gray-700"><strong className="text-gray-800">Site Location:</strong> {user.location}</p>
          <p className="text-gray-700"><strong className="text-gray-800">Phone Number:</strong> {user.bio}</p>
          <p className="text-gray-700"><strong className="text-gray-800">Member Since:</strong> {new Date(user.joinedDate).toLocaleDateString()}</p>
          <p className="text-gray-700"><strong className="text-gray-800">Invite Emails:</strong> {user.invites || inviteList.join(', ')}</p>
          <button
            className="bg-blue-600 text-white px-4 py-2 rounded-md font-semibold mt-4 hover:bg-blue-700 transition-colors"
            onClick={() => setIsEditing(true)}
          >
            Edit Profile
          </button>
        </div>
      )}
    </ProfileSection>
  );
};

export default ProfileDetails;
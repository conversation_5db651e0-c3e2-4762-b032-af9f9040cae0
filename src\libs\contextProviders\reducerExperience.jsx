// reducerExperience.js
export const INITIAL_EXPERIENCE_STATE={
    firstPersonView:true,
    showCart:false,
    showWishlist:false,
    mode360:true,
    modeModel:false,
    modeAR:false,
    textureIndex:false,
    activeRoomSnap: null, // New state for active room snap
    levelToHide: null, // New state for active room snap
}

export const ACTIONS_EXPERIENCE={
    MODE_360:'MODE_360',
    MODE_MODEL:'MODE_MODEL',
    MODE_AR:'MODE_AR',
    TOGGLE_CART:'TOGGLE_CART',
    TOGGLE_WISHLIST:'TOGGLE_WISHLIST',
    ADD_TO_CART:'ADD_TO_CART',
    TEXTURE_INDEX:'TEXTURE_INDEX',
    SET_FIRST_PERSON_VIEW: 'SET_FIRST_PERSON_VIEW', // New action
    SET_ACTIVE_ROOM_SNAP: 'SET_ACTIVE_ROOM_SNAP',   // New action
    LEVEL_TO_HIDE: 'LEVEL_TO_HIDE',   // New action
}

export const reducerExperience=(state,action)=>{
    switch (action.type) {
        case 'MODE_360':
            return{
                ...state,
                mode360:true,
                modeModel:false,
                modeAR:false,
                firstPersonView:true,
                activeRoomSnap: null, // Clear active room snap when switching modes
            }
        case 'MODE_MODEL':
            return{
                ...state,
                mode360:false,
                modeModel:true,
                modeAR:false,
                firstPersonView:false, // Default to false when entering 3D mode
                activeRoomSnap: null, // Clear active room snap when switching modes
            }
        case 'MODE_AR':
            return{
                ...state,
                mode360:false,
                modeModel:false,
                modeAR:true,
                activeRoomSnap: null, // Clear active room snap when switching modes
            }
        case 'TOGGLE_CART':
            return{
                ...state,
                showCart:!state?.showCart,
            }
        case 'TEXTURE_INDEX':
            return{
                ...state,
                textureIndex:action?.payload,
            }
        case 'TOGGLE_WISHLIST':
            return{
                ...state,
                showWishlist:!state?.showWishlist
            }
        case 'ADD_TO_CART':
            return{
                ...state,
            }
        case 'SET_FIRST_PERSON_VIEW': // New case
            return {
                ...state,
                firstPersonView: action.payload,
            }
        case 'SET_ACTIVE_ROOM_SNAP': // New case
            return {
                ...state,
                activeRoomSnap: action.payload,
            }
        case 'LEVEL_TO_HIDE': // New case
            return {
                ...state,
                levelToHide: action.payload,
            }
        default:
            return state;
    }
}
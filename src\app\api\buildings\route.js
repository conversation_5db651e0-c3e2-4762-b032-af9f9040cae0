// src/app/api/buildings/route.js
// API routes for building CRUD operations

import { NextResponse } from "next/server";
import { auth } from "../../../auth";
import dbConnect from "../../../libs/mongoDb/connectToLuyariDB";
import { Building } from "../../../libs/mongoDb/models/Building";

/**
 * GET /api/buildings - List all buildings with pagination
 * Query parameters:
 * - page: Page number (default: 1)
 * - limit: Items per page (default: 10)
 * - search: Search term for projectTitle or buildingTitle
 * - buildingType: Filter by building type
 */
export async function GET(request) {
  try {
    const session = await auth();
    
    if (!session || !session.user) {
      return NextResponse.json(
        { message: "Authentication required" },
        { status: 401 }
      );
    }

    await dbConnect();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 10;
    const search = searchParams.get('search') || '';
    const buildingType = searchParams.get('buildingType') || '';

    // Build query
    const query = {};
    
    if (search) {
      query.$or = [
        { projectTitle: { $regex: search, $options: 'i' } },
        { buildingTitle: { $regex: search, $options: 'i' } },
        { desc: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (buildingType) {
      query.buildingType = buildingType;
    }

    // Calculate pagination
    const skip = (page - 1) * limit;
    
    // Get buildings with pagination
    const buildings = await Building.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Get total count for pagination
    const total = await Building.countDocuments(query);
    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      buildings,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error("Error fetching buildings:", error);
    return NextResponse.json(
      { message: "Internal server error", error: error.message },
      { status: 500 }
    );
  }
}

/**
 * POST /api/buildings - Create new building
 */
export async function POST(request) {
  try {
    const session = await auth();
    
    if (!session || !session.user) {
      return NextResponse.json(
        { message: "Authentication required" },
        { status: 401 }
      );
    }

    await dbConnect();

    const buildingData = await request.json();

    // Validate required fields
    const requiredFields = [
      'projectTitle', 'price', 'buildingTitle', 'buildingType', 
      'desc', 'position', 'arPosition', 'minDistance', 'maxDistance', 
      'features', 'outroSection', 'buildingSummary'
    ];

    for (const field of requiredFields) {
      if (!buildingData[field]) {
        return NextResponse.json(
          { message: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Validate buildingType
    const validBuildingTypes = ['multi-storey', 'single-storey', 'multi-residence'];
    if (!validBuildingTypes.includes(buildingData.buildingType)) {
      return NextResponse.json(
        { message: "Invalid building type" },
        { status: 400 }
      );
    }

    // Validate buildingSummary structure
    const requiredSummaryFields = ['length', 'width', 'baths', 'levels', 'cars', 'beds'];
    for (const field of requiredSummaryFields) {
      if (buildingData.buildingSummary[field] === undefined || buildingData.buildingSummary[field] === null) {
        return NextResponse.json(
          { message: `Missing required buildingSummary field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Check for duplicate projectTitle
    const existingBuilding = await Building.findOne({ 
      projectTitle: buildingData.projectTitle 
    });
    
    if (existingBuilding) {
      return NextResponse.json(
        { message: "A building with this project title already exists" },
        { status: 409 }
      );
    }

    // Create new building
    const building = new Building(buildingData);
    await building.save();

    return NextResponse.json(
      { 
        message: "Building created successfully", 
        building: building.toObject() 
      },
      { status: 201 }
    );

  } catch (error) {
    console.error("Error creating building:", error);
    
    if (error.code === 11000) {
      return NextResponse.json(
        { message: "A building with this project title already exists" },
        { status: 409 }
      );
    }
    
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => err.message);
      return NextResponse.json(
        { message: "Validation error", errors: validationErrors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error", error: error.message },
      { status: 500 }
    );
  }
}

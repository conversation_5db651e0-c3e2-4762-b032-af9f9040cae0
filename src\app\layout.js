import { Gei<PERSON>, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";
import { Providers } from "./providers";
import NavbarComponent from "@/components/NavbarComponent";
import AdminLink from "@/components/AdminLink";
import { settings } from "@/libs/siteSettings";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata = {
  title: settings.siteName,
  description: settings.siteMaxim,
};

export default async function RootLayout({ children }) {
  
  return (
    <html lang="en" className="w-full h-screen">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <Providers>
          <NavbarComponent/>
          {children}
        </Providers>
        <AdminLink/>
      </body>
    </html>
  );
}

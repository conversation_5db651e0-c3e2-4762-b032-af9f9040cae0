// middleware.js
// This middleware protects routes based on authentication status and user roles.

import NextAuth from "next-auth";
import { NextResponse } from "next/server";
// FIX: Corrected import path for auth.config.js relative to middleware.js (which is in project root)
import { authConfig } from "./auth.config";

const { auth } = NextAuth(authConfig);

export default auth((req) => {
  const { nextUrl } = req;
  const isLoggedIn = !!req.auth;

  const protectedRoutes = ["/dashboard"];
  const adminProtectedRoutes = ["/admin"];

  const isAdminRoute = adminProtectedRoutes.some((route) =>
    nextUrl.pathname.startsWith(route)
  );

  if (!isLoggedIn && (protectedRoutes.includes(nextUrl.pathname) || isAdminRoute)) {
    return NextResponse.redirect(new URL("/auth/signin", nextUrl));
  }

  if (isLoggedIn && isAdminRoute) {
    const userRole = req.auth.user.role;
    if (userRole !== "admin") {
      return NextResponse.redirect(new URL("/not-authorized", nextUrl));
    }
  }

  return NextResponse.next();
});

export const config = {
  matcher: ["/((?!api|_next/static|_next/image|favicon.ico|auth).*)"],
};

// lib/nodemailer.js
// This file contains the logic for sending verification and password reset emails.

import nodemailer from "nodemailer";

// Create a Nodemailer transporter using SMTP details from environment variables
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_SERVER_HOST,
  port: process.env.EMAIL_SERVER_PORT,
  secure: false, // true for 465, false for other ports (e.g., 587 for TLS)
  auth: {
    user: process.env.EMAIL_SERVER_USER,
    pass: process.env.EMAIL_SERVER_PASSWORD,
  },
});

/**
 * Sends a verification email for the magic link provider.
 * @param {object} params - Parameters for the verification email.
 * @param {string} params.identifier - The user's email address.
 * @param {string} params.url - The magic link URL.
 * @param {object} params.provider - The email provider configuration.
 */
export async function sendVerificationRequest({ identifier: email, url, provider }) {
  const { host } = new URL(url);

  try {
    const result = await transporter.sendMail({
      to: email,
      from: provider.from,
      subject: `Sign in to ${host}`,
      text: `Sign in to ${host}\n${url}\n\n`,
      html: `
        <div style="font-family: sans-serif; line-height: 1.6; color: #333;">
          <h2 style="color: #0056b3;">Sign in to ${host}</h2>
          <p>Click the link below to sign in:</p>
          <p style="margin: 20px 0;">
            <a href="${url}" style="display: inline-block; padding: 10px 20px; background-color: #007bff; color: #ffffff; text-decoration: none; border-radius: 5px;">
              Sign in
            </a>
          </p>
          <p>If you did not request this, you can ignore this email.</p>
          <p style="font-size: 0.9em; color: #777;">This link expires in 24 hours.</p>
        </div>
      `,
    });
    console.log("Verification email sent:", result);
  } catch (error) {
    console.error("Error sending verification email:", error);
    throw new Error("Failed to send verification email.");
  }
}

/**
 * Sends a password reset email.
 * @param {object} params - Parameters for the password reset email.
 * @param {string} params.email - The user's email address.
 * @param {string} params.resetLink - The password reset link URL.
 */
export async function sendPasswordResetEmail({ email, resetLink }) {
  try {
    const result = await transporter.sendMail({
      to: email,
      from: process.env.EMAIL_FROM,
      subject: `Password Reset Request`,
      text: `You requested a password reset. Click the link to reset your password:\n${resetLink}\n\nThis link will expire in 1 hour.`,
      html: `
        <div style="font-family: sans-serif; line-height: 1.6; color: #333;">
          <h2 style="color: #0056b3;">Password Reset Request</h2>
          <p>You recently requested to reset your password for your account.</p>
          <p>Click the button below to reset your password:</p>
          <p style="margin: 20px 0;">
            <a href="${resetLink}" style="display: inline-block; padding: 10px 20px; background-color: #dc3545; color: #ffffff; text-decoration: none; border-radius: 5px;">
              Reset Password
            </a>
          </p>
          <p>If you did not request a password reset, please ignore this email.</p>
          <p style="font-size: 0.9em; color: #777;">This link will expire in 1 hour.</p>
        </div>
      `,
    });
    console.log("Password reset email sent:", result);
  } catch (error) {
    console.error("Error sending password reset email:", error);
    throw new Error("Failed to send password reset email.");
  }
}

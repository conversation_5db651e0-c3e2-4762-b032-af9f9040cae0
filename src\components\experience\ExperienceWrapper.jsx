'use client'

import React, { Suspense } from 'react'
import ExperienceUi from './ExperienceUi'
// import ExperienceWorld from './ExperienceWorld'
import dynamic from 'next/dynamic'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'

const ExperienceWorld = dynamic(() => import('./ExperienceWorld'),{ssr:false})

export default function ExperienceWrapper({data}) {
  const {experienceState,experienceDispatch}=useExperienceContext()
  // console.log('ExperienceWrapper:',data)
  return (
    <div className='flex relative w-full h-full'>
      <ExperienceUi data={data}/>
      <Suspense fallback={null}>
        <ExperienceWorld data={data}/>
      </Suspense>
    </div>
  )
}

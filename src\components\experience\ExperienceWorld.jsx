'use client'
import { Canvas } from '@react-three/fiber'
import React, { Suspense } from 'react'
import ExperienceAR from './ExperienceAR'
import { Environment } from '@react-three/drei'
import Experience360 from './Experience360'
import ExperienceModel from './ExperienceModel'
import ExperienceControls from './ExperienceControls'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'

export default function ExperienceWorld({data}) {
  const {experienceState,experienceDispatch}=useExperienceContext()
  // console.log('ExperienceWorld:',experienceState)
  return (
    <Canvas>
      <Suspense fallback={null}>
        {experienceState?.modeAR
          ? <ExperienceAR data={data}/>
          : <>
              <ExperienceControls data={data} /> {/* Pass experienceDispatch */}
              {experienceState?.mode360 && <Experience360 data={data}/>}
              {experienceState?.modeModel && <ExperienceModel data={data}/>}
            </>
        }
      </Suspense>
    </Canvas>
  )
}
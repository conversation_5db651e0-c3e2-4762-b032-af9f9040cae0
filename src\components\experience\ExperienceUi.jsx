'use client'
import React, { useState } from 'react'

import Image from 'next/image'
import { usePathname } from 'next/navigation'
import { TbAugmentedReality2, TbHexagon3D } from 'react-icons/tb';// Import ACTIONS_EXPERIENCE
import { FaAngleLeft, FaAngleRight } from 'react-icons/fa6';
import { TbView360 } from "react-icons/tb";
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext';
import { ACTIONS_EXPERIENCE } from '@/libs/contextProviders/reducerExperience';

export default function ExperienceUi({data}) {
  const pathname=usePathname()
  const [toggleLevel,setToggleLevel]=useState(false)
  const [widenoptions,setWidenoptions]=useState(false)
  const { experienceState,experienceDispatch } = useExperienceContext();

  const cssIcon='flex cursor-pointer hover:scele-105 duration-300 ease-linear md:text-4xl text-3xl border-2 border-gray-50 rounded-full p-1 min-w-8 md:w-10 min-h-8 md:h-10'
  const cssTitle='flex md:text-sm text-xs text-center w-full justify-center font-medium'
  const btnStyle='flex bg-slate-800 text-xs capitalize hover:underline duration-300 ease-linear cursor-pointer items-center justify-center relative w-full h-10 text-wrap text-center'
  const btnClass='flex text-sm font-medium z-10 items-center justify-center absolute top-16 md:top-20 select-none left-0 right-0 mx-auto w-fit h-10 rounded-lg bg-black/50 gap-1 p-1'

  const modes=[
    {icon:<TbView360 className={cssIcon}/>},
    {icon:<TbHexagon3D className={cssIcon}/>},
    {icon:<TbAugmentedReality2 className={cssIcon}/>},
  ]

  const handleModes = (index) => {
    index==0 && experienceDispatch({type:ACTIONS_EXPERIENCE.MODE_360})
    index==1 && experienceDispatch({type:ACTIONS_EXPERIENCE.MODE_MODEL})
    index==2 && experienceDispatch({type:ACTIONS_EXPERIENCE.MODE_AR})

    // Reset firstPersonView and activeRoomSnap when switching to 3D mode
    if (index === 1) {
      experienceDispatch({type: ACTIONS_EXPERIENCE.SET_FIRST_PERSON_VIEW, payload: false});
      experienceDispatch({type: ACTIONS_EXPERIENCE.SET_ACTIVE_ROOM_SNAP, payload: null});
    }
  }

  const handle360Index = (index) => {
    experienceDispatch({type: ACTIONS_EXPERIENCE.TEXTURE_INDEX, payload: index});// Enable first person view when snapping
  }

  const handleRoomSnapClick = (roomSnap) => {
    experienceDispatch({type: ACTIONS_EXPERIENCE.SET_ACTIVE_ROOM_SNAP, payload: roomSnap});
    experienceDispatch({type: ACTIONS_EXPERIENCE.SET_FIRST_PERSON_VIEW, payload: true}); // Enable first person view when snapping
  }

  const handleLevelToHideClick = (levelToHoide) => {
    setToggleLevel(!toggleLevel)
    experienceDispatch({type: ACTIONS_EXPERIENCE.LEVEL_TO_HIDE, payload: {toggleLevelState:toggleLevel,name:levelToHoide}});
    // experienceDispatch({type: ACTIONS_EXPERIENCE.SET_FIRST_PERSON_VIEW, payload: true}); // Enable first person view when snapping
  }

  return (
    <>
      {/* modes menu */}
      <div className='flex z-10 max-w-fit absolute left-0 right-0 mx-auto top-20 text-white w-full gap-1 bg-black/50 items-center capitalize h-fit flex-col shadow rounded-xl p-1'>
        <div className='flex w-full h-fit p-1 bg-black/50 rounded-xl gap-2 overflow-x-auto overflow-hidden'>
          {modes.map((i,index)=>
            <div onClick={()=>handleModes(index)} key={index} className='flex  text-sm items-center justify-center relative w-fit h-fit'>
              {i?.icon}
            </div>
          )}
        </div>
      </div>

      {/* ui title */}
      {experienceState?.activeRoomSnap?.length>0 && <div className='flex z-10 max-w-fit absolute right-4 mx-auto top-32 text-white w-full gap-1 bg-black/50 items-center capitalize h-fit flex-col shadow rounded-xl p-2'>
        <span className='text-sm'>{experienceState?.activeRoomSnap}</span>
      </div>}

      {/* options menu */}
      <div className={`z-10 ${widenoptions ? 'md:w-1/6 w-28' : 'md:w-24 w-20'} h-full absolute my-auto text-white top-0 left-0 p-2 select-none duration-300 ease-linear`}>
        <div className='flex relative w-full h-full items-center'>
          <div onClick={()=>setWidenoptions(!widenoptions)} className='flex z-20 absolute text-gray-500 w-fit h-fit -right-4 cursor-pointer rounded-full bg-gray-100 p-1 shadow'>
            {widenoptions ? <FaAngleLeft className='text-2xl'/> : <FaAngleRight className='text-2xl'/>}
          </div>
          <div className='flex w-full min-h-4 md:max-h-2/3 max-h-1/2 rounded-2xl shadow-md items-start bg-gray-200/35 gap-2 flex-col p-1 top-0 bottom-0 my-auto overflow-y-auto overflow-hidden'>
            <div className='flex w-full gap-1 bg-gray-200/50 h-fit flex-col rounded-xl'>
              {/* The _360s Images menu */}
              {data?._360sImages?.length>0 && <div className='flex w-full gap-1 bg-black/75 h-fit flex-col capitalize items-center shadow rounded-xl p-1'>
                <span className={cssTitle}>360s list</span>
                <div className='flex w-full h-fit p-1 bg-black/50 rounded-xl flex-wrap gap-1'>
                  {data?._360sImages?.map((i,index)=>
                    <div onClick={()=> handle360Index(index)} key={i?.name} className='flex cursor-pointer bg-slate-800 items-center rounded-md overflow-hidden justify-center relative w-full min-h-10 md:min-h-12'>
                      <span className='absolute z-10 text-xs bottom-1 mx-auto'>{widenoptions ? i?.name : index}</span>
                      <Image className='object-cover brightness-75' src={i?.url} alt='360 images' fill/>
                    </div>
                  )}
                </div>
              </div>}
              {/* The hide levels menu */}
              {data?.hideLevel?.length>0 && <div className='flex w-full gap-1 bg-black/50 h-fit flex-col capitalize items-center shadow rounded-xl p-1'>
                <span className={cssTitle}>level toggle</span>
                <div className='flex w-full h-fit p-1 bg-slate-900 rounded-xl flex-wrap gap-1'>
                  {data?.hideLevel?.map((i,index)=>
                    <div key={i?.name} className={btnStyle} onClick={()=> handleLevelToHideClick(i?.name)}>
                      {widenoptions ? i?.name : index}
                    </div>
                  )}
                </div>
              </div>}
              {/* The colors menu */}
              {data?.color?.length>0 && <div className='flex w-full gap-1 bg-black/50 h-fit flex-col capitalize items-center shadow rounded-xl p-1'>
                <span className={cssTitle}>colors</span>
                <div className='flex w-full h-fit p-1 bg-slate-900 rounded-xl flex-wrap gap-1'>
                  {data?.color?.map(i=>
                    <div key={i} className={btnStyle}>
                      {i}
                    </div>
                  )}
                </div>
              </div>}
              {/* The room snap menu */}
              {data?.roomSnaps?.length>0 &&  <div className='flex w-full gap-1 bg-black/75 h-fit flex-col capitalize items-center shadow rounded-xl p-1'>
                <span className={cssTitle}>3d views</span>
                <div className='flex w-full h-fit p-1 bg-slate-900 rounded-xl flex-wrap gap-1'>
                  {data?.roomSnaps?.map((i,index)=>
                    <div key={i?.name} className={btnStyle} onClick={()=> handleRoomSnapClick(i?.name)}> 
                      {widenoptions ? i?.name : index}
                    </div>
                  )}
                </div>
              </div>}
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
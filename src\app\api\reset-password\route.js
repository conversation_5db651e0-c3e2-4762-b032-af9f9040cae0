// src/app/api/auth/reset-password/route.js
// This API route handles the actual password reset using a token.

import { NextResponse } from "next/server";
// FIX: Corrected import path for mongodb-client-promise.js
// Path from route.js to src/libs/mongoDb/mongodb-client-promise.js:
// route.js -> reset-password -> auth -> api -> app -> src -> libs/mongoDb/mongodb-client-promise.js
import clientPromise from "../../../../../../libs/mongoDb/mongodb-client-promise";
import bcrypt from "bcryptjs";

export async function POST(request) {
  try {
    const { token, newPassword } = await request.json();

    if (!token || !newPassword) {
      return NextResponse.json(
        { message: "Token and new password are required." },
        { status: 400 }
      );
    }

    const client = await clientPromise;
    const db = client.db();
    const usersCollection = db.collection("users");

    const user = await usersCollection.findOne({
      resetToken: token,
      resetTokenExpiry: { $gt: Date.now() },
    });

    if (!user) {
      return NextResponse.json(
        { message: "Invalid or expired password reset token." },
        { status: 400 }
      );
    }

    const hashedPassword = await bcrypt.hash(newPassword, 10);

    await usersCollection.updateOne(
      { _id: user._id },
      {
        $set: { password: hashedPassword },
        $unset: { resetToken: "", resetTokenExpiry: "" },
      }
    );

    return NextResponse.json(
      { message: "Password has been reset successfully." },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error in reset password API:", error);
    return NextResponse.json(
      { message: "An internal server error occurred while resetting your password." },
      { status: 500 }
    );
  }
}

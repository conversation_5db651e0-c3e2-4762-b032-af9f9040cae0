// src/app/api/admin/users/route.js
// This API route fetches all users for the admin dashboard.

import { NextResponse } from "next/server";
// FIX: Corrected import path for mongodb-client-promise.js
// Path from route.js to src/libs/mongoDb/mongodb-client-promise.js:
// route.js -> users -> admin -> api -> app -> src -> libs/mongoDb/mongodb-client-promise.js
import clientPromise from "../../../../../libs/mongoDb/mongodb-client-promise";
// FIX: Corrected import path for auth.js
// Path from route.js to src/auth.js:
// route.js -> users -> admin -> api -> app -> src -> auth.js
import { auth } from "../../../../../auth";

export async function GET(request) {
  try {
    const session = await auth();

    if (!session || !session.user) {
      console.warn("Unauthorized attempt to access admin users API: No session found.");
      return NextResponse.json(
        { message: "Authentication required to access this resource." },
        { status: 401 }
      );
    }

    if (session.user.role !== "admin") {
      console.warn(`Forbidden attempt to access admin users API by user: ${session.user.email} (Role: ${session.user.role})`);
      return NextResponse.json(
        { message: "Access denied. You do not have sufficient privileges to perform this action." },
        { status: 403 }
      );
    }

    const client = await clientPromise;
    const db = client.db();
    const usersCollection = db.collection("users");

    const users = await usersCollection.find({}, { projection: { password: 0 } }).toArray();

    const serializedUsers = users.map(user => ({
      ...user,
      _id: user._id.toString(),
    }));

    return NextResponse.json({ users: serializedUsers }, { status: 200 });
  } catch (error) {
    console.error("Server Error: Failed to fetch users for admin dashboard.", error);
    return NextResponse.json(
      { message: "An unexpected server error occurred while fetching user data. Please try again later." },
      { status: 500 }
    );
  }
}

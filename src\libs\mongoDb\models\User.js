// models/User.js
import { Timestamp } from 'mongodb';
import mongoose from 'mongoose';

const UserSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please provide a name.'],
    maxlength: [60, 'Name cannot be more than 60 characters'],
  },
  email: {
    type: String,
    required: [true, 'Please provide an email.'],
    unique: true,
    match: [
      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
      'Please provide a valid email.',
    ],
  },
  password: {
    type: String,
    // Password is not required for OAuth/Magic Link users
    required: function() { return !this.accounts || this.accounts.length === 0; },
    minlength: [6, 'Password must be at least 6 characters'],
  },
  role: {
    type: String,
    enum: ['user', 'admin'],
    default: 'user',
  },
  image: String, // For OAuth providers
  emailVerified: Date, // For Email provider
  // NextAuth.js will add accounts and sessions fields automatically for the adapter
}, { timestamps: true });

export default mongoose.models.User || mongoose.model('User', UserSchema);

// app/dashboard/page.js
// This is a simple dashboard page accessible by any authenticated user.

"use client"; // This component runs on the client side

import { useSession, signOut } from "next-auth/react";
import Link from "next/link";

export default function DashboardPage() {
  const { data: session, status } = useSession();

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <p className="text-xl text-gray-700">Loading session...</p>
      </div>
    );
  }

  if (status === "unauthenticated") {
    // This case should ideally be handled by middleware, but good for fallback
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <p className="text-xl text-red-600">You need to be signed in to view this page.</p>
        <Link href="/auth/signin" className="ml-4 text-blue-500 hover:underline">Sign In</Link>
      </div>
    );
  }

  // If authenticated, display user information
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-100 p-4">
      <div className="bg-white p-8 rounded-lg shadow-md text-center w-full max-w-md">
        <h1 className="text-3xl font-bold text-gray-800 mb-6">User Dashboard</h1>

        {session && (
          <div className="text-left mb-6">
            <p className="text-lg text-gray-700 mb-2">
              Welcome, <span className="font-semibold">{session.user.name || session.user.email}</span>!
            </p>
            <p className="text-gray-600">Email: {session.user.email}</p>
            <p className="text-gray-600">Role: <span className="font-medium capitalize">{session.user.role}</span></p>
            <p className="text-gray-600">User ID: {session.user.id}</p>
          </div>
        )}

        <div className="flex flex-col space-y-4">
          {session?.user?.role === "admin" && (
            <Link href="/admin/dashboard" className="w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded-lg transition duration-200 text-center">
              Go to Admin Dashboard
            </Link>
          )}
          <button
            onClick={() => signOut({ callbackUrl: "/" })} // Redirect to home after sign out
            className="w-full bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg focus:outline-none focus:shadow-outline transition duration-200"
          >
            Sign Out
          </button>
        </div>
      </div>
    </div>
  );
}

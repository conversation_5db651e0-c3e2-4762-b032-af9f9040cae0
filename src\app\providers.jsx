"use client";

import { SessionProvider } from "next-auth/react";
import SiteContextProvider from "@/libs/contextProviders/useSiteContext";
import SiteExperienceContextProvider from "@/libs/contextProviders/useSiteExperienceContext";

export function Providers({ children }) {
  return (
    <SessionProvider>
      <SiteContextProvider>
        <SiteExperienceContextProvider>
          {children}
        </SiteExperienceContextProvider>
      </SiteContextProvider>
    </SessionProvider>
  );
}
